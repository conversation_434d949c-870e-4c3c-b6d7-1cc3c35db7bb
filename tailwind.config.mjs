/** @type {import('tailwindcss').Config} */
export default {
  darkMode: 'class',
  content: [
    './src/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    extend: {
      colors: {
        arcane: {
          primary: '#5e2aff',
          secondary: '#a78bfa',
          dark: '#1e1b4b',
          accent: '#c084fc',
          light: '#ede9fe',
        },
      },
      fontFamily: {
        arcane: ['"Cinzel Decorative"', 'serif'],
      },
      boxShadow: {
        arcane: '0 4px 14px 0 rgba(94, 42, 255, 0.39)',
      }
    },
  },
  plugins: [],
}