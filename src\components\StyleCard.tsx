"use client";
import React from 'react';
import { motion } from 'framer-motion';
import { tv } from 'tailwind-variants';

interface StyleCardProps {
  styleName: string;
  isSelected: boolean;
  onSelect: (style: string) => void;
}

const cardVariants = tv({
  base: 'relative flex items-center justify-center p-4 rounded-lg cursor-pointer transition-all duration-300 ease-in-out',
  variants: {
    selected: {
      true: 'bg-arcane-primary text-white shadow-lg shadow-arcane-primary/50 scale-105',
      false: 'bg-gray-800 text-gray-300 hover:bg-gray-700 hover:scale-105',
    },
  },
});

const StyleCard: React.FC<StyleCardProps> = ({ styleName, isSelected, onSelect }) => {
  return (
    <motion.div
      whileHover={{ scale: 1.02 }}
      whileTap={{ scale: 0.98 }}
      className={cardVariants({ selected: isSelected })}
      onClick={() => onSelect(styleName)}
    >
      <span className="text-lg font-semibold font-arcane">{styleName}</span>
    </motion.div>
  );
};

export default StyleCard;