import { GoogleGenerativeA<PERSON>, HarmCategory, HarmBlockThreshold } from "@google/generative-ai";
import { NextResponse } from "next/server";

export const runtime = 'edge';

export async function POST(request: Request) {
  try {
    const formData = await request.formData();
    const image = formData.get('image') as File;
    const style = formData.get('style') as string;

    if (!image || !style) {
      return NextResponse.json({ error: 'Missing image or style' }, { status: 400 });
    }

    // Convert image to base64
    const buffer = await image.arrayBuffer();
    const base64Image = Buffer.from(buffer).toString('base64');

    // Initialize Gemini
    const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY!);
    const model = genAI.getGenerativeModel({ model: "gemini-2.0-flash" });

    // Generate image
    const result = await model.generateContent({
      contents: [
        { role: "user", parts: [{ text: `Apply ${style} style to this image` }, { inlineData: { data: base64Image, mimeType: image.type } }] }
      ],
      generationConfig: {
        responseMimeType: "application/json"
      },
      safetySettings: [{
        category: HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT,
        threshold: HarmBlockThreshold.BLOCK_ONLY_HIGH
      }]
    });

    // Assuming Gemini returns a JSON with a 'transformedImage' field
    const responseData = JSON.parse(result.response.text());
    const transformedImageUrl = responseData.transformedImage || 'https://via.placeholder.com/500x500.png?text=Transformed+Image';

    return NextResponse.json({ image: transformedImageUrl });
  } catch (error) {
    console.error(error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}