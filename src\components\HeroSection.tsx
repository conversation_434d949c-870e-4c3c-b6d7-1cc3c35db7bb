"use client";
import React from 'react';
import { motion } from 'framer-motion';

const HeroSection: React.FC = () => {
  return (
    <section className="relative h-screen flex items-center justify-center text-center bg-arcane-dark text-arcane-light overflow-hidden">
      <div className="absolute inset-0 z-0">
        {/* Placeholder for a magical background effect */}
        <div className="w-full h-full bg-gradient-to-br from-purple-900 via-indigo-900 to-black opacity-70"></div>
      </div>
      <motion.div
        initial={{ opacity: 0, y: 50 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8 }}
        className="z-10 max-w-3xl mx-auto p-8"
      >
        <h1 className="text-6xl font-arcane font-bold mb-6 leading-tight">
          Arcane Artify
        </h1>
        <p className="text-xl mb-8 opacity-90">
          Transform your images with dark-magical style transfers, powered by Google Gemini 2.0 Flash.
        </p>
        <button className="bg-arcane-primary hover:bg-arcane-secondary text-white font-bold py-3 px-8 rounded-full shadow-arcane transition-all duration-300">
          Get Started
        </button>
      </motion.div>
    </section>
  );
};

export default HeroSection;