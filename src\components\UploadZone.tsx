"use client";
import React, { useState, useCallback } from 'react';
import { useDropzone } from 'react-dropzone';
import { motion } from 'framer-motion';
import { ImageUp, Wand2, Loader2 } from 'lucide-react';
import Image from 'next/image'; // Added import for next/image
import StyleCard from './StyleCard';
import { uploadImage, saveImageRecord } from '../lib/supabase'; // Corrected import path

const styles = [
  'Mystic Veil', 'Shadow Weave', 'Arcane Glyph', 'Eldritch Rune',
  'Celestial Bloom', 'Nebula Shroud', 'Abyssal Echo', 'Lunar Halo',
  'Solar Flare', 'Void Tracer', 'Dream Weaver', 'Phoenix Ember',
  'Frostbite Sigil', 'Thunderstorm Crest', 'Enchanted Vine'
];

const UploadZone: React.FC = () => {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [selectedStyle, setSelectedStyle] = useState<string | null>(null);
  const [transformedImage, setTransformedImage] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const onDrop = useCallback((acceptedFiles: File[]) => {
    setSelectedFile(acceptedFiles[0]);
    setTransformedImage(null);
    setError(null);
  }, []);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'image/jpeg': [],
      'image/png': [],
      'image/webp': [],
    },
    multiple: false,
  });

  const handleTransform = async () => {
    if (!selectedFile || !selectedStyle) {
      setError('Please select an image and a style.');
      return;
    }

    setLoading(true);
    setError(null);
    setTransformedImage(null);

    try {
      // // Upload original image to Supabase
      // const originalImageUrl = await uploadImage(selectedFile);

      const formData = new FormData();
      formData.append('image', selectedFile);
      formData.append('style', selectedStyle);

      const response = await fetch('/api/generate', {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Image transformation failed.');
      }

      const data = await response.json();
      const transformedImageUrl = data.image; // Assuming the API returns the image URL directly

      // // Save transformed image to Supabase (if needed, or handle directly from API)
      // // For now, we'll assume the API returns a public URL or a data URL.
      setTransformedImage(transformedImageUrl);

      // // Save record to database (assuming a dummy user ID for now)
      // await saveImageRecord('dummy-user-id', originalImageUrl, transformedImageUrl, selectedStyle);

    } catch (err) {
      setError((err as Error).message || 'An unexpected error occurred.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <section className="container mx-auto p-8 bg-gray-900 rounded-lg shadow-xl text-arcane-light my-12">
      <h2 className="text-4xl font-arcane font-bold text-center mb-8">Transform Your Art</h2>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
        {/* Upload Section */}
        <motion.div
          initial={{ opacity: 0, x: -50 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.6 }}
          className="bg-gray-800 p-6 rounded-lg"
        >
          <h3 className="text-2xl font-semibold mb-4">Upload Image</h3>
          <div
            {...getRootProps()}
            className={`border-2 border-dashed p-12 rounded-lg text-center cursor-pointer transition-colors duration-200 ${
              isDragActive ? 'border-arcane-primary bg-gray-700' : 'border-gray-600 hover:border-gray-400'
            }`}
          >
            <input {...getInputProps()} />
            <ImageUp className="mx-auto w-16 h-16 text-gray-400 mb-4" />
            {selectedFile ? (
              <p className="text-lg text-arcane-light">Selected: {selectedFile.name}</p>
            ) : (
              <p className="text-lg text-gray-400">Drag &apos;n&apos; drop an image here, or click to select one</p>
            )}
          </div>
          {selectedFile && (
            <div className="mt-4 text-center">
              <Image src={URL.createObjectURL(selectedFile)} alt="Selected" width={500} height={500} className="max-w-full h-auto rounded-lg mx-auto" />
            </div>
          )}
        </motion.div>

        {/* Style Selection */}
        <motion.div
          initial={{ opacity: 0, x: 50 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          className="bg-gray-800 p-6 rounded-lg"
        >
          <h3 className="text-2xl font-semibold mb-4">Choose Style</h3>
          <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 gap-4 max-h-96 overflow-y-auto pr-2">
            {styles.map((style) => (
              <StyleCard
                key={style}
                styleName={style}
                isSelected={selectedStyle === style}
                onSelect={setSelectedStyle}
              />
            ))}
          </div>
        </motion.div>
      </div>

      {/* Action Button & Result */}
      <motion.div
        initial={{ opacity: 0, y: 50 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.4 }}
        className="mt-8 text-center"
      >
        {error && <p className="text-red-500 mb-4">{error}</p>}
        <button
          onClick={handleTransform}
          disabled={loading || !selectedFile || !selectedStyle}
          className="bg-arcane-accent hover:bg-purple-600 text-white font-bold py-3 px-10 rounded-full shadow-arcane transition-all duration-300 flex items-center justify-center mx-auto"
        >
          {loading ? (
            <>
              <Loader2 className="animate-spin mr-2" /> Transforming...
            </>
          ) : (
            <>
              <Wand2 className="mr-2" /> Transform Image
            </>
          )}
        </button>

        {transformedImage && (
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.5 }}
            className="mt-8"
          >
            <h3 className="text-2xl font-semibold mb-4">Transformed Image</h3>
            <Image src={transformedImage} alt="Transformed" width={500} height={500} className="max-w-full h-auto rounded-lg mx-auto shadow-lg" />
          </motion.div>
        )}
      </motion.div>
    </section>
  );
};

export default UploadZone;