import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;
export const supabase = createClient(supabaseUrl, supabaseKey);

export async function uploadImage(file: File): Promise<string> {
  const fileName = `${Date.now()}-${file.name}`;
  const { data, error } = await supabase.storage
    .from('images')
    .upload(fileName, file);

  if (error) throw new Error('Upload failed');

  const { data: { publicUrl } } = supabase.storage
    .from('images')
    .getPublicUrl(data.path);

  return publicUrl;
}

export async function saveImageRecord(userId: string, originalUrl: string, transformedUrl: string, style: string) {
  const { error } = await supabase
    .from('images')
    .insert({ user_id: userId, original_url: originalUrl, transformed_url: transformedUrl, style });
  
  if (error) throw new Error('Failed to save image record');
}