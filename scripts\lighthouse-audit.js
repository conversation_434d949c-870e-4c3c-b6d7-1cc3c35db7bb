import { chromium } from 'playwright';
import { supabase } from '../src/lib/supabase';

(async () => {
  const browser = await chromium.launch();
  const page = await browser.newPage();
  await page.goto('https://arcaneartify.vercel.app');
  
  // Run Lighthouse audit
  const { report } = await page.evaluate(() => {
    return new Promise(resolve => {
      window.__lighthouseAudit = (result) => resolve(result);
      const script = document.createElement('script');
      script.src = 'https://cdn.jsdelivr.net/npm/lighthouse@9.6.8/lighthouse.min.js';
      document.head.appendChild(script);
    });
  });

  // Save results to Supabase
  await supabase
    .from('lighthouse_history')
    .insert({
      performance_score: report.categories.performance.score,
      accessibility_score: report.categories.accessibility.score,
      best_practices_score: report.categories['best-practices'].score,
      seo_score: report.categories.seo.score
    });

  await browser.close();
})();