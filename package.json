{"name": "arcane-artify", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "test": "vitest", "test:e2e": "playwright test", "lint": "next lint", "lighthouse-audit": "node scripts/lighthouse-audit.js", "cron": "node -e \"setInterval(() => require('child_process').exec('pnpm lighthouse-audit'), 60*60*1000)\""}, "dependencies": {"@google/generative-ai": "0.13.0", "@supabase/supabase-js": "2.46.2", "autoprefixer": "^10.4.21", "axios": "^1.9.0", "framer-motion": "^12.15.0", "lucide-react": "^0.511.0", "motion": "12.10.5", "next": "15.3.2", "patch-package": "^8.0.0", "postcss": "^8.5.6", "react": "^19.1.0", "react-dom": "^19.1.0", "react-dropzone": "^14.3.8", "react-masonry-css": "^1.0.16", "stripe": "12", "tailwind-variants": "^1.0.0", "tailwindcss": "^3.4.17", "zod": "^3.25.34", "zustand": "^5.0.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@playwright/test": "^1.52.0", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@vitejs/plugin-react": "^4.5.0", "eslint": "^9", "eslint-config-next": "15.3.2", "jsdom": "^26.1.0", "playwright": "^1.52.0", "typescript": "^5", "vitest": "^3.1.4"}}