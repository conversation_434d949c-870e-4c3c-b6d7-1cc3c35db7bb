@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --font-cinzel-decorative: "Cinzel Decorative";
  }
}

body {
  font-family: var(--font-cinzel-decorative), serif;
}

/* Custom Masonry Grid Styles */
.my-masonry-grid {
  display: -webkit-box; /* Not needed if autoprefixing */
  display: -ms-flexbox; /* Not needed if autoprefixing */
  display: flex;
  margin-left: -30px; /* gutter size offset */
  width: auto;
}
.my-masonry-grid_column {
  padding-left: 30px; /* gutter size */
  background-clip: padding-box;
}

/* Style your items */
.my-masonry-grid_column > div { /* change div to all your item classes for masonry */
  margin-bottom: 30px;
}
