'use client';

import React, { useEffect, useState } from 'react';
import Masonry from 'react-masonry-css';
import { createClient } from '@supabase/supabase-js';
import { motion } from 'framer-motion';
import Image from 'next/image';

// Initialize Supabase client (ensure env variables are set)
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!; // Use anon key for client-side
const supabase = createClient(supabaseUrl, supabaseKey);

interface ImageRecord {
  id: string;
  original_url: string;
  transformed_url: string;
  style: string;
  created_at: string;
}

const breakpointColumnsObj = {
  default: 4,
  1100: 3,
  700: 2,
  500: 1
};

const GalleryPage: React.FC = () => {
  const [images, setImages] = useState<ImageRecord[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchImages = async () => {
      try {
        const { data, error } = await supabase
          .from('images')
          .select('*')
          .order('created_at', { ascending: false })
          .limit(50); // Fetch latest 50 images

        if (error) throw error;
        setImages(data || []);
      } catch (err: unknown) {
        setError((err as Error).message || 'Failed to fetch images.');
      } finally {
        setLoading(false);
      }
    };

    fetchImages();
  }, []);

  if (loading) {
    return <div className="min-h-screen flex items-center justify-center text-arcane-light">Loading gallery...</div>;
  }

  if (error) {
    return <div className="min-h-screen flex items-center justify-center text-red-500">Error: {error}</div>;
  }

  return (
    <div className="container mx-auto p-8 bg-gray-950 text-arcane-light min-h-screen">
      <h1 className="text-5xl font-arcane font-bold text-center mb-12">Art Gallery</h1>
      {images.length === 0 ? (
        <div className="text-center text-xl text-gray-400">No transformed images yet. Start transforming on the home page!</div>
      ) : (
        <Masonry
          breakpointCols={breakpointColumnsObj}
          className="my-masonry-grid"
          columnClassName="my-masonry-grid_column"
        >
          {images.map((image) => (
            <motion.div
              key={image.id}
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.3 }}
              className="relative rounded-lg overflow-hidden shadow-lg group"
            >
              <Image
                src={image.transformed_url}
                alt={`Transformed image with ${image.style} style`}
                width={500} // Adjust based on expected image sizes
                height={500} // Adjust based on expected image sizes
                layout="responsive"
                objectFit="cover"
                className="w-full h-auto"
              />
              <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                <p className="text-white text-lg font-semibold">{image.style}</p>
              </div>
            </motion.div>
          ))}
        </Masonry>
      )}
    </div>
  );
};

export default GalleryPage;