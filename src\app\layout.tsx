import type { Metadata } from "next";
import { Cinzel_Decorative } from "next/font/google";
import "./globals.css";
import Link from "next/link";

const cinzel_decorative = Cinzel_Decorative({
  weight: ["400", "700", "900"],
  subsets: ["latin"],
  variable: "--font-cinzel-decorative",
});

export const metadata: Metadata = {
  title: "Arcane Artify",
  description: "Transform your images with dark-magical style transfers, powered by Google Gemini 2.0 Flash.",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className={`${cinzel_decorative.variable}`}>
      <body className="bg-gray-950 text-arcane-light">
        <nav className="p-4 bg-gray-900 shadow-lg flex justify-between items-center">
          <Link href="/" className="text-2xl font-arcane font-bold text-arcane-primary">
            Arcane Artify
          </Link>
          <div className="space-x-4">
            <Link href="/" className="text-arcane-light hover:text-arcane-accent transition-colors duration-200">
              Home
            </Link>
            <Link href="/gallery" className="text-arcane-light hover:text-arcane-accent transition-colors duration-200">
              Gallery
            </Link>
          </div>
        </nav>
        {children}
      </body>
    </html>
  );
}
